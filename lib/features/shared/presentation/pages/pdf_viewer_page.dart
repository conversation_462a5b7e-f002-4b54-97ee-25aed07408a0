// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:printing/printing.dart';

import '../../../../core/config/logger/app_logger.dart';
import '../../../../core/utils/helpers/snack_bar_helper.dart';
import '../widgets/animations/animated_app_bar.dart';

///  PDF Viewer
class PdfViewerPage extends StatelessWidget {
  final Uint8List pdfBytes;
  final Function()? onDownload;
  final String title;
  const PdfViewerPage({
    super.key,
    required this.pdfBytes,
    required this.onDownload,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return Scaffold(
      appBar: AnimatedAppBar(
        appBarHeight: 60,
        leading: IconButton(
          onPressed: () => context.popRoute(),
          icon: Icon(
            Icons.arrow_back_ios_new_rounded,
            color: appColors.whiteColor,
          ),
        ),
        title: title,
        style: textTheme.titleLarge?.copyWith(
          color: appColors.whiteColor,
          fontSize: 16,
        ),
        actions: [
          // _buildActionButton(
          //   context: context,
          //   // icon: isDownloading ? Icons.download_done : Icons.download,
          //   icon: Icons.download,
          //   tooltip: "Download PDF",
          //   onPressed: () => onDownload,
          // ),
          _buildActionButton(
            context: context,
            icon: Icons.share,
            tooltip: 'Share PDF',
            onPressed: () => _sharePDF(context: context),
          ),
          _buildActionButton(
            context: context,
            // icon: Icons.qr_code,
            icon: Icons.print,
            tooltip: 'Print',
            onPressed: () => _handlePDFPrint(context: context),
          ),
          const SizedBox(width: 10),
        ],
      ),
      // body: SfPdfViewer.memory(pdfBytes),
      body: PdfPreview(
        build: (format) => pdfBytes,
        useActions: false,
      ),
    );
  }

  _buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    final appColors = context.appColors;
    return IconButton(
      tooltip: tooltip,
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: appColors.whiteColor,
      ),
    );
  }

  /// Handle PDF Print
  Future<void> _handlePDFPrint({
    required BuildContext context,
  }) async {
    try {
      await Printing.layoutPdf(
        onLayout: (format) async {
          return pdfBytes;
        },
      );
    } catch (e, s) {
      AppLogger().error(
        'Error printing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error printing PDF: $e',
      );
    }
  }

  // Function to share PDF directly without downloading
  Future<void> _sharePDF({
    required BuildContext context,
  }) async {
    try {
      // // Create a temporary file
      // final directory = await getTemporaryDirectory();
      final uniqueFileName =
          'appointment_${DateTime.now().millisecondsSinceEpoch}.pdf';
      // final tempFile = File('${directory.path}/$uniqueFileName');

      // // Write the PDF bytes to the temporary file
      // await tempFile.writeAsBytes(pdfBytes);

      // // Share the PDF using share_plus with the created temporary file
      // await Share.shareXFiles(
      //   [XFile(tempFile.path)],
      //   text: 'Check out this appointment PDF',
      // );
      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: uniqueFileName,
        subject: 'Appointment PDF',
      );
    } catch (e, s) {
      AppLogger().error(
        'Error Sharing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error Sharing PDF: $e',
      );
    }
  }
}
