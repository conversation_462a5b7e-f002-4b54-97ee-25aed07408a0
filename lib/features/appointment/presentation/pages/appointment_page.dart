import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/config/router/extension/navigation_extension.dart';
import 'package:hodan_hospital/core/config/theme/colors/app_colors_extension.dart';
import 'package:hodan_hospital/core/enums/loading_type.dart';
import 'package:hodan_hospital/core/utils/extensions/app_bloc_extensions.dart';
import 'package:hodan_hospital/core/utils/extensions/build_context_extensions.dart';
import 'package:hodan_hospital/core/utils/helpers/snack_bar_helper.dart';
import 'package:hodan_hospital/features/appointment/domain/entities/appointment_entity.dart';
import 'package:hodan_hospital/features/appointment/presentation/bloc/appointment_bloc.dart';
import 'package:hodan_hospital/features/appointment/presentation/widgets/appointment_widget.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/animations/animated_app_bar.dart';
import 'package:hodan_hospital/features/shared/presentation/widgets/custom_list_grid_view.dart';
import 'package:printing/printing.dart';

import 'scan_appointment_page.dart';

class AppointmentPage extends StatefulWidget {
  const AppointmentPage({super.key});

  @override
  State<AppointmentPage> createState() => _AppointmentPageState();
}

class _AppointmentPageState extends State<AppointmentPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAppointments();
    });
  }

  Future<void> _loadAppointments({bool forceFetch = false}) async {
    final previousAppointments = context.appointmentBloc.previousAppointments;
    final upcomingAppointments = context.appointmentBloc.upcomingAppointments;
    final canRefetch = forceFetch ||
        (previousAppointments.isEmpty && upcomingAppointments.isEmpty);

    if (canRefetch) {
      context.appointmentBloc.add(FetchAppointmentEvent(
        mobileNo: context.userBloc.currentUser?.phoneNumber ?? '',
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final appointmentBloc = context.appointmentBloc;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AnimatedAppBar(
          appBarHeight: 100,
          title: 'Appointments',
          style: textTheme.titleMedium?.copyWith(
            color: appColors.whiteColor,
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(0),
            child: TabBar(
              labelStyle: textTheme.bodyMedium?.copyWith(
                color: appColors.whiteColor,
              ),
              unselectedLabelStyle: textTheme.bodyMedium?.copyWith(
                color: appColors.whiteColor.withValues(alpha: 0.5),
              ),
              tabs: const [
                Tab(
                  text: 'Upcoming',
                ),
                Tab(
                  text: 'History',
                ),
              ],
            ),
          ),
        ),
        body: TabBarView(
          children: [
            buildAppointments(appointmentBloc: appointmentBloc),
            buildAppointments(
                appointmentBloc: appointmentBloc, isHistory: true),
          ],
        ),
      ),
    );
  }

  BlocConsumer<AppointmentBloc, AppointmentState> buildAppointments({
    required AppointmentBloc appointmentBloc,
    bool isHistory = false,
  }) {
    return BlocConsumer<AppointmentBloc, AppointmentState>(
      listener: handleAppointmentListener,
      builder: (context, state) {
        final List<AppointmentEntity> filteredAppointments = isHistory
            ? appointmentBloc.previousAppointments
            : appointmentBloc.upcomingAppointments;

        final isLoading = state is AppointmentLoading;
        final isEmpty = state is AppointmentEmpty;
        return RefreshIndicator(
          onRefresh: () async {
            await _loadAppointments(forceFetch: true);
          },
          child: CustomListGridView(
            items: filteredAppointments,
            isLoading: isLoading,
            isEmpty: isEmpty,
            contentType: LoadingType.listView,
            emptyWidgetMessage: 'No Appointment avaialable.',
            itemBuilder: (context, appointment) {
              //
              return ApointmentWidget(
                key: ValueKey(appointment.appointmentID),
                appointment: appointment,
                onDownload: () {
                  handleAppointmentPDFGet(
                    appointment: appointment,
                    appointmentBloc: appointmentBloc,
                  );
                },
              );
            },
            onRefresh: () async {
              //
              appointmentBloc.add(FetchAppointmentEvent(
                mobileNo: context.userBloc.currentUser?.phoneNumber ?? '',
                forceFetch: true,
              ));
            },
          ),
        );
      },
    );
  }

  /// Handle Appointment Listener
  void handleAppointmentListener(
    BuildContext context,
    AppointmentState state,
  ) {
    //
    // if (state is AppointmentSuccess) {
    //   SnackBarHelper.showSuccessSnackBar(context, message: state.message);
    // }

    if (state is AppointmentFailure) {
      final message = state.failure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(
        context,
        message: message,
      );
    }

    ///------------------------ PDF Get ------------------------
    if (state is GetAppointmentPDFSuccess) {
      //
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => AppointmentPDFViewer(pdfBytes: state.pdfBytes),
      //   ),
      // );
      context.pushRoute(
        // PdfViewerPage(
        //   pdfBytes: state.pdfBytes,
        //   onDownload: () {
        //     handlePDFDownload(appointmentBloc: context.appointmentBloc);
        //   },
        //   title: 'Appointment PDF',
        // ),
        AppointmentPDFViewer(pdfBytes: state.pdfBytes),
      );
    }

    if (state is GetAppointmentPDFFailure) {
      //
      final message = state.failure.getErrorMessage();
      SnackBarHelper.showErrorSnackBar(
        context,
        message: message,
      );
    }
  }

  /// Handle Appointment PDF Get
  Future<void> handleAppointmentPDFGet({
    required AppointmentEntity appointment,
    required AppointmentBloc appointmentBloc,
  }) async {
    //
    appointmentBloc.add(GetAppointmentPDFEvent(
      appointmentId: appointment.appointmentID,
    ));
  }
}

/// Appointment PDF Viewer
class AppointmentPDFViewer extends StatelessWidget {
  final Uint8List pdfBytes;
  const AppointmentPDFViewer({super.key, required this.pdfBytes});

  @override
  Widget build(BuildContext context) {
    final appointmentBloc = context.appointmentBloc;
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return BlocConsumer<AppointmentBloc, AppointmentState>(
      listener: (context, state) {
        //   if (state is ProcessAppointmentBarCodeLoading) {
        //   context.dialogCubit.showLoadingDialog();
        // }
        if (state is ProcessAppointmentBarCodeSuccess) {
          // context.dialogCubit.closeDialog();

          //
          SnackBarHelper.showSuccessSnackBar(
            context,
            // message: state.message,
            message: 'Appointment Bar Code successfully processed',
          );
        }
        if (state is ProcessAppointmentBarCodeFailure) {
          // context.dialogCubit.closeDialog();

          //
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.failure.getErrorMessage(),
          );
        }

        ///------------------------ PDF Download ------------------------

        //
        if (state is DownloadAppointmentPDFSuccess) {
          //
          SnackBarHelper.showSuccessSnackBar(
            context,
            message: 'Appointment PDF downloaded successfully',
          );
        }

        //
        if (state is DownloadAppointmentPDFFailure) {
          //
          SnackBarHelper.showErrorSnackBar(
            context,
            message: state.failure.getErrorMessage(),
          );
        }
      },
      builder: (context, state) {
        final isDownloading = state is DownloadAppointmentPDFLoading;
        return Scaffold(
          appBar: AnimatedAppBar(
            appBarHeight: 60,
            leading: IconButton(
              onPressed: () => context.popRoute(),
              icon: Icon(
                Icons.arrow_back_ios_new_rounded,
                color: appColors.whiteColor,
              ),
            ),
            title: 'Appointment PDF',
            style: textTheme.titleLarge?.copyWith(
              color: appColors.whiteColor,
              fontSize: 16,
            ),
            actions: [
              // buildActionButton(
              //   context: context,
              //   icon: isDownloading ? Icons.download_done : Icons.download,
              //   tooltip: isDownloading ? 'Downloading...' : 'Download PDF',
              //   onPressed: () => handlePDFDownload(
              //     appointmentBloc: appointmentBloc,
              //   ),
              // ),
              buildActionButton(
                context: context,
                icon: Icons.share,
                tooltip: 'Share PDF',
                onPressed: () => sharePDF(context: context),
              ),
              buildActionButton(
                context: context,
                // icon: Icons.qr_code,
                icon: Icons.qr_code_scanner,
                tooltip: 'Scan & Print Appointment',
                // onPressed: () => _handlePDFPrint(context: context),
                onPressed: () => handleScanAppointment(context: context),
              ),
              const SizedBox(width: 10),
            ],
          ),
          // body: SfPdfViewer.memory(pdfBytes),
          body: PdfPreview(
            build: (format) => pdfBytes,
            useActions: false,
          ),
        );
      },
    );
  }

  buildActionButton({
    required BuildContext context,
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    final appColors = context.appColors;
    return IconButton(
      tooltip: tooltip,
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: appColors.whiteColor,
      ),
    );
  }

  /// Handle Scan Appointment
  Future<void> handleScanAppointment({
    required BuildContext context,
  }) async {
    try {
      final scannedValue = await Navigator.push<String>(
        context,
        MaterialPageRoute(
          builder: (context) => const ScanAppointmentPage(),
        ),
      );

      if (scannedValue != null) {
        SnackBarHelper.showInfoSnackBar(
          context,
          message: 'Processing appointment $scannedValue...',
        );

        await handlePDFPrint(context: context);
      } else {
        SnackBarHelper.showErrorSnackBar(
          context,
          message: 'Please scan the correct bar code',
        );
      }
    } catch (e, s) {
      AppLogger().error(
        'Error scanning appointment: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error scanning appointment: $e',
      );
    }
  }

  /// Handle PDF Print
  Future<void> handlePDFPrint({
    required BuildContext context,
  }) async {
    try {
      await Printing.layoutPdf(
        onLayout: (format) async {
          return pdfBytes;
        },
      );
    } catch (e, s) {
      AppLogger().error(
        'Error printing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error printing PDF: $e',
      );
    }
  }

  // Function to share PDF directly without downloading
  Future<void> sharePDF({
    required BuildContext context,
  }) async {
    try {
      // // Create a temporary file
      // final directory = await getTemporaryDirectory();
      final uniqueFileName =
          'appointment_${DateTime.now().millisecondsSinceEpoch}.pdf';
      // final tempFile = File('${directory.path}/$uniqueFileName');

      // // Write the PDF bytes to the temporary file
      // await tempFile.writeAsBytes(pdfBytes);

      // // Share the PDF using share_plus with the created temporary file
      // await Share.shareXFiles(
      //   [XFile(tempFile.path)],
      //   text: 'Check out this appointment PDF',
      // );
      await Printing.sharePdf(
        bytes: pdfBytes,
        filename: uniqueFileName,
        subject: 'Appointment PDF',
      );
    } catch (e, s) {
      AppLogger().error(
        'Error Sharing PDF: $e',
        error: e,
        stackTrace: s,
      );
      SnackBarHelper.showErrorSnackBar(
        context,
        message: 'Error Sharing PDF: $e',
      );
    }
  }

  /// Handle PDF Download
  Future<void> handlePDFDownload({
    required AppointmentBloc appointmentBloc,
  }) async {
    //
    appointmentBloc.add(DowloadAppointmentPDFEvent(
      pdfBytes: pdfBytes,
    ));
  }
}
