// ignore_for_file: unnecessary_null_comparison

import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/config/enviroment/enviroment_config.dart';
import 'package:hodan_hospital/core/config/logger/app_logger.dart';
import 'package:hodan_hospital/core/constants/api_end_points.dart';
import 'package:hodan_hospital/core/enums/cache_failure_type.dart';
import 'package:hodan_hospital/core/enums/http_failure_type.dart';
import 'package:hodan_hospital/core/enums/http_method.dart';
import 'package:hodan_hospital/core/enums/payment_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/utils/helpers/request_data.dart';
import 'package:hodan_hospital/core/utils/helpers/response_handler.dart';
import 'package:hodan_hospital/features/appointment/data/models/appointment_model.dart';

import '../../../../../core/config/di/dependency_injection.dart';
import '../../../../../core/constants/analytics_events.dart';
import '../../../../../core/services/analytics_service.dart';
import '../../../../../core/services/payment_service.dart';

abstract class AppointmentRemoteDataSource {
  FutureEitherFailOr<List<AppointmentModel>> getAppointments({
    required String mobileNo,
  });
  FutureEitherFailOr<String> makeAppointments({
    required String pID,
    required String doctorPractitioner,
    required String patientMobile,
    required double doctAmount,
    required bool hasFollowUp,
    required String appointmentDate,
    required bool hasMembership,
  });

  FutureEitherFailOr<String> processAppointmentBarCode({
    required String queName,
  });

  FutureEitherFailOr<String> downloadAppointmentPDF({
    required Uint8List pdfBytes,
  });

  FutureEitherFailOr<Uint8List> getAppointmentPDF({
    required String appointmentId,
  });
}

class AppointmentRemoteDataSourceImpl implements AppointmentRemoteDataSource {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final PaymentService paymentService;

  AppointmentRemoteDataSourceImpl({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.paymentService,
  });

  final AnalyticsService _analyticsService = sl<AnalyticsService>();

  @override
  FutureEitherFailOr<String> processAppointmentBarCode({
    required String queName,
  }) async {
    final response = await httpErrorHandler.handleRequest<String>(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.processAppointmentBarCode,
        queryParameters: {
          'que_name': queName,
        },
      ),
    );

    return ResponseHandler<String>(response).handleResponse(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data.apiMessage),
    );
  }

  @override
  FutureEitherFailOr<Uint8List> getAppointmentPDF({
    required String appointmentId,
  }) async {
    final response = await httpErrorHandler.handleRequestBytes(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndpoints.getPDF,
        responseType: ResponseType.bytes,
        queryParameters: {
          'doctype': 'Que',
          'format': 'Que Printing',
          'name': appointmentId,
        },
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (bytes) {
        return right(bytes);
      },
    );
  }

  @override
  FutureEitherFailOr<String> downloadAppointmentPDF({
    required Uint8List pdfBytes,
  }) async {
    try {
      // Define the download path
      final time = DateTime.now().millisecondsSinceEpoch;
      final path = '/storage/emulated/0/Download/appointment_$time.pdf';

      // Save the PDF file
      final file = File(path);
      await file.writeAsBytes(pdfBytes);

      // Log and show success message
      AppLogger().info('✅ PDF saved at: $path');

      return right(path);
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error downloading PDF : $error',
        error: error,
        stackTrace: stackTrace,
      );
      return left(CacheFailure(
        message: 'Error initializing file download : $error',
        failureType: CacheFailureType.unknown,
        stackTrace: stackTrace,
      ));
    }
  }

  @override
  FutureEitherFailOr<List<AppointmentModel>> getAppointments({
    required String mobileNo,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => AppointmentModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndpoints.getAppointment,
          data: RequestData.json({
            'mobile_no': mobileNo,
          })),
    );

    return ResponseHandler<List<AppointmentModel>>(response)
        .handleResponseAndExtractData(
      onFailure: (failure) => left(failure),
      onSuccess: (data) => right(data),
    );
  }

  //?---  Priority: Follow-up (free) > Membership (50%) > Full Price
  @override
  FutureEitherFailOr<String> makeAppointments({
    required String pID,
    required String doctorPractitioner,
    required String patientMobile,
    required double doctAmount,
    required bool hasFollowUp,
    required String appointmentDate,
    required bool hasMembership,
  }) async {
    // log the request data
    AppLogger().info(
      'Request Data: pid =$pID, doctorName = $doctorPractitioner, patientNumber : $patientMobile',
    );
    final cancelToken = CancelToken();

    final url = EnvironmentConfig.hodanMerchantApiUrl;

    // IF he have membership card make him 50% discount
    final amountToPay = hasMembership ? doctAmount * 0.5 : doctAmount;

    final payload = _createPayload(
      serviceName: 'API_PURCHASE',
      patientMobile: patientMobile,
      // amount: amountToPay, // for production
      amount: 0.01, // for testing
    );

    final canBookResponse = await _canBookAppointment(
      pID: pID,
      doctorPractitioner: doctorPractitioner,
      doctAmount: amountToPay,
      appointmentDate: appointmentDate,
    );
    return canBookResponse.fold(
      (failure) {
        //
        return left(failure);
      },
      (message) async {
        if (!hasFollowUp) {
          await _analyticsService.logPaymentInfo(
            paymentType: 'MWALLET_ACCOUNT',
            currency: 'USD',
            value: amountToPay,
            parameters: {
              'context': 'Appointment',
              'event': AnalyticsEvents.payment_initiated,
              'amountToPay': amountToPay,
              'patientMobile': patientMobile,
              'pID': pID,
              'doctorPractitioner': doctorPractitioner,
              'doctAmount': doctAmount,
              'appointmentDate': appointmentDate,
              'hasMembership': hasMembership,
              'hasFollowUp': hasFollowUp,
            },
          );

          final response = await httpErrorHandler.handlePaymentRequest(
            requestFunction: () => dioApiClient.request(
              method: HttpMethod.post,
              endPointUrl: url,
              data: RequestData.json(payload),
              receiveTimeout: const Duration(seconds: 50),
              cancelToken: cancelToken,
            ),
          );

          return response.fold(
            (failure) async {
              await _analyticsService.logPaymentInfo(
                paymentType: 'MWALLET_ACCOUNT',
                currency: 'USD',
                value: amountToPay,
                parameters: {
                  'context': 'Appointment',
                  'event': AnalyticsEvents.payment_failed,
                  'amountToPay': amountToPay,
                  'patientMobile': patientMobile,
                  'pID': pID,
                  'doctorPractitioner': doctorPractitioner,
                  'doctAmount': doctAmount,
                  'appointmentDate': appointmentDate,
                  'hasMembership': hasMembership,
                  'hasFollowUp': hasFollowUp,
                  'failureMessage': failure.getErrorMessage(),
                  'failure': failure.toString(),
                },
              );
              //
              cancelToken.cancel(
                  'Request cancelled due to failure'); // Cancel on failure

              return left(failure);
            },
            (success) async {
              String? transactionId;

              // Extract orderId from successful payment
              if (success['params'] is Map) {
                transactionId = success['params']?['transactionId']?.toString();
              }

              await _analyticsService.logPaymentInfo(
                paymentType: 'MWALLET_ACCOUNT',
                currency: 'USD',
                value: amountToPay,
                parameters: {
                  'context': 'Appointment',
                  'event': AnalyticsEvents.payment_successful,
                  'transactionId': transactionId ?? '',
                  'amountToPay': amountToPay,
                  'patientMobile': patientMobile,
                  'pID': pID,
                  'doctorPractitioner': doctorPractitioner,
                  'doctAmount': doctAmount,
                  'appointmentDate': appointmentDate,
                  'hasMembership': hasMembership,
                  'hasFollowUp': hasFollowUp,
                },
              );

              final appointmentResult = await _saveAppointment(
                pID: pID,
                doctorPractitioner: doctorPractitioner,
                doctAmount: amountToPay,
                hasFollowUp: hasFollowUp,
                appointmentDate: appointmentDate,
              );

              return appointmentResult.fold(
                (appointmentFailure) async {
                  // 3. Reverse payment if appointment fails
                  if (transactionId != null) {
                    await _handlePaymentReversal(
                      transactionId: transactionId,
                      patientMobile: patientMobile,
                      amount: doctAmount,
                    );

                    await _analyticsService.logPaymentInfo(
                      paymentType: 'MWALLET_ACCOUNT',
                      currency: 'USD',
                      value: doctAmount,
                      parameters: {
                        'context': 'Appointment',
                        'event': AnalyticsEvents.payment_cancelled,
                        'transactionId': transactionId,
                        'amountToPay': doctAmount,
                        'patientMobile': patientMobile,
                        'pID': pID,
                        'doctorPractitioner': doctorPractitioner,
                        'doctAmount': doctAmount,
                        'appointmentDate': appointmentDate,
                        'hasMembership': hasMembership,
                        'hasFollowUp': hasFollowUp,
                      },
                    );
                  }
                  return left(appointmentFailure);
                },
                (success) async {
                  // Reverse payment if appointment fails for development only
                  if (transactionId != null) {
                    await _handlePaymentReversal(
                      transactionId: transactionId,
                      patientMobile: patientMobile,
                      amount: doctAmount,
                    );

                    await _analyticsService.logPaymentInfo(
                      paymentType: 'MWALLET_ACCOUNT',
                      currency: 'USD',
                      value: doctAmount,
                      parameters: {
                        'context': 'Appointment',
                        'event': AnalyticsEvents.payment_refunded,
                        'transactionId': transactionId,
                        'amountToPay': doctAmount,
                        'patientMobile': patientMobile,
                        'pID': pID,
                        'doctorPractitioner': doctorPractitioner,
                        'doctAmount': doctAmount,
                        'appointmentDate': appointmentDate,
                        'hasMembership': hasMembership,
                        'hasFollowUp': hasFollowUp,
                      },
                    );
                  }
                  return right(success);
                },
              );
            },
          );
        }

        ///-------- Follow-up number booking no payment is needed
        else {
          final appointmentResult = await _saveAppointment(
            pID: pID,
            doctorPractitioner: doctorPractitioner,
            doctAmount: amountToPay,
            hasFollowUp: hasFollowUp,
            appointmentDate: appointmentDate,
          );

          return appointmentResult.fold(
            (appointmentFailure) async {
              return left(appointmentFailure);
            },
            (success) async {
              return right(success);
            },
          );
        }

        // return right(message);
      },
    );
  }

  FutureEitherFailOr<String> _saveAppointment({
    required String pID,
    required String doctorPractitioner,
    required double doctAmount,
    required bool hasFollowUp,
    required String appointmentDate,
  }) async {
    AppLogger().info(
        '💾 Saving appointment: pID=$pID, doctor=$doctorPractitioner, amount=$doctAmount');

    try {
      final response = await httpErrorHandler.handleRequest(
        requestFunction: () => dioApiClient.request(
          method: HttpMethod.post,
          endPointUrl: ApiEndpoints.makeAppointment,
          data: RequestData.json(
            {
              'PID': pID,
              'doctor_practitioner': doctorPractitioner,
              'doct_amount': doctAmount,
              'appointment_date': appointmentDate,
              'is_followup': hasFollowUp,
            },
          ),
          sendTimeout: const Duration(seconds: 50),
          receiveTimeout: const Duration(seconds: 50),
        ),
      );

      return response.fold(
        (failure) => left(failure),
        (apiResponse) {
          final apiMessage = apiResponse.apiMessage;
          return right(apiMessage);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error saving appointment ',
        error: error,
        stackTrace: stackTrace,
      );
      return left(HttpFailure(
        message: 'Failed to save appointment : $error',
        failureType: HttpFailureType.serverError,
      ));
    }
  }

  FutureEitherFailOr<String> _canBookAppointment({
    required String pID,
    required String doctorPractitioner,
    required double doctAmount,
    required String appointmentDate,
  }) async {
    //
    try {
      final response = await httpErrorHandler.handleRequest(
        requestFunction: () => dioApiClient.request(
          method: HttpMethod.post,
          endPointUrl: ApiEndpoints.canBookAppointment,
          data: RequestData.json(
            {
              'PID': pID,
              'doctor_practitioner': doctorPractitioner,
              'doct_amount': doctAmount,
              'appointment_date': appointmentDate,
            },
          ),
          sendTimeout: const Duration(seconds: 30),
          receiveTimeout: const Duration(seconds: 30),
        ),
      );

      return response.fold(
        (failure) => left(failure),
        (apiResponse) {
          final apiMessage = apiResponse.apiMessage;
          return right(apiMessage);
        },
      );
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error Checking if can book an appointment :  $error ',
        error: error,
        stackTrace: stackTrace,
      );
      return left(HttpFailure(
        message: 'Failed to book appointment appointment : $error',
        failureType: HttpFailureType.serverError,
      ));
    }
  }

  FutureEitherFailOr<String> _handlePaymentReversal({
    required String transactionId,
    required String patientMobile,
    required double amount,
  }) async {
    AppLogger()
        .info('Attempting to reverse payment for orderId: $transactionId');

    try {
      final response = await dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: EnvironmentConfig.hodanMerchantApiUrl,
        data: RequestData.json(
          _createPayload(
            serviceName: 'API_CANCELPURCHASE',
            patientMobile: patientMobile,
            amount: amount,
            transactionId: transactionId,
          ),
        ),
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final isSuccess = responseData['responseCode'] == '2001' ||
            responseData['responseCode'] == '2000';

        if (isSuccess) {
          AppLogger().info('✅ Payment reversal successful');
          return left(PaymentFailure(
            message:
                'Appointment failed but payment has been reversed. Please try again.',
            failureType: PaymentFailureType.transactionError,
          ));
        }
      }

      AppLogger().error('❌ Payment reversal failed');
      return left(PaymentFailure(
        message:
            'Appointment failed and payment reversal failed. Please contact support.',
        failureType: PaymentFailureType.transactionError,
        statusCode: response.statusCode,
        apiMessage: response.data['responseMsg']?.toString(),
      ));
    } catch (error) {
      AppLogger().error('Error during payment reversal', error: error);
      return left(PaymentFailure(
        message: 'Failed to reverse payment: $error',
        failureType: PaymentFailureType.transactionError,
      ));
    }
  }

  /// Create a payload for the payment request
  Map<String, Object> _createPayload({
    required String serviceName,
    required String patientMobile,
    required double amount,
    String? transactionId, // Optional transactionId: Used for reversal
  }) {
    final serviceParams = {
      'merchantUid': EnvironmentConfig.hodanMerchantUid,
      // "merchantUid": EnvironmentConfig.rasiinMerchantUid,
      'apiUserId': EnvironmentConfig.hodanMerchantApiUserId,
      // "apiUserId": EnvironmentConfig.rasiinMerchantApiUserId,
      'apiKey': EnvironmentConfig.hodanMerchantApiKey,
      // "apiKey": EnvironmentConfig.rasiinMerchantApiKey,
      'paymentMethod': 'MWALLET_ACCOUNT',
      'payerInfo': {'accountNo': patientMobile},
    };

    // Add transaction-specific parameters based on service type
    if (serviceName == 'API_PURCHASE') {
      serviceParams['transactionInfo'] = {
        'referenceId': '********',
        'invoiceId': '********',
        'amount': amount,
        'currency': 'USD',
        'description': 'Medical Appointment Payment'
      };
    } else {
      // For API_CANCELPURCHASE
      serviceParams.addAll({
        'transactionId': transactionId ?? '',
        'referenceId': 'REF-${DateTime.now().millisecondsSinceEpoch}',
        'description': 'Reversal due to appointment saving failure'
      });
    }

    return {
      'schemaVersion': '1.0',
      'requestId': '***********',
      'timestamp': '2024-06-2 Africa',
      'channelName': 'WEB',
      'serviceName': serviceName,
      'serviceParams': serviceParams,
    };
  }
}
