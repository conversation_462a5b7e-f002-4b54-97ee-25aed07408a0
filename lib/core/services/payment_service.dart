import 'package:dio/dio.dart';
import 'package:fpdart/fpdart.dart';
import 'package:hodan_hospital/core/constants/analytics_events.dart';
import 'package:hodan_hospital/core/enums/payment_failure_type.dart';
import 'package:hodan_hospital/core/errors/app_failure.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/analytics_service.dart';

import '../config/enviroment/enviroment_config.dart';
import '../config/logger/app_logger.dart';
import '../enums/http_method.dart';
import '../utils/helpers/request_data.dart';

class PaymentService {
  final DioApiClient dioApiClient;
  final AnalyticsService analytics;

  const PaymentService({required this.dioApiClient, required this.analytics});

  static const purchase = 'API_PURCHASE';
  static const cancelPurchase = 'API_CANCELPURCHASE';

  /// Process a payment with comprehensive analytics tracking
  FutureEitherFailOr<String> processPayment({
    required double amount,
    required String patientMobile,
    required String context,
    required Map<String, dynamic> contextData,
    CancelToken? cancelToken,
    bool isTesting = false, // for development and testing(auto reversal)
  }) async {
    final paymentId = 'pay_${DateTime.now().millisecondsSinceEpoch}';
    final startTime = DateTime.now();

    try {
      final endpoint = EnvironmentConfig.hodanMerchantApiUrl;

      // Track payment initiation
      await analytics.logPaymentInfo(
        paymentType: 'MWALLET_ACCOUNT',
        currency: 'USD',
        value: amount,
        parameters: {
          'event': AnalyticsEvents.payment_initiated,
          'custom_payment_id': paymentId,
          'context': context,
          'timestamp': startTime.toIso8601String(),
          ...contextData,
        },
      );

      final payload = _createPayload(
        serviceName: purchase,
        patientMobile: patientMobile,
        amount: amount,
      );

      final response = await dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: endpoint,
        data: RequestData.json(payload),
        cancelToken: cancelToken,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;
      final data = response.data;

      final responseCode = data['responseCode'];
      final isSuccess = responseCode == '2001' || responseCode == '2000';
      final transactionId = _extractTransactionId(data);

      // Log result based on success or failure
      await analytics.logPaymentInfo(
        paymentType: 'MWALLET_ACCOUNT',
        currency: 'USD',
        value: amount,
        parameters: {
          'event': isSuccess
              ? AnalyticsEvents.payment_successful
              : AnalyticsEvents.payment_failed,
          'custom_payment_id': paymentId,
          'transaction_id': transactionId,
          'context': context,
          'duration_ms': duration,
          'timestamp': endTime.toIso8601String(),
          'responseCode': responseCode ?? 'null',
          'responseMsg': data['responseMsg'] ?? '',
          ...contextData,
        },
      );

      if (isSuccess) {
        if (isTesting) {
          await processRefund(
            transactionId: transactionId,
            patientMobile: patientMobile,
            amount: amount,
          );
        }

        return right(transactionId);
      } else {
        return left(PaymentFailure(
          message: 'Payment failed: ${data['responseMsg'] ?? 'Unknown error'}',
          failureType: PaymentFailureType.transactionError,
          statusCode: response.statusCode,
          apiMessage: data['responseMsg']?.toString(),
        ));
      }
    } catch (error, stackTrace) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime).inMilliseconds;

      await analytics.logPaymentInfo(
        paymentType: 'MWALLET_ACCOUNT',
        currency: 'USD',
        value: amount,
        parameters: {
          'event': AnalyticsEvents.payment_failed,
          'custom_payment_id': paymentId,
          'context': context,
          'duration_ms': duration,
          'timestamp': endTime.toIso8601String(),
          'error': error.toString(),
          'stack_trace': stackTrace.toString(),
          ...contextData,
        },
      );

      return left(PaymentFailure(
        message: 'Payment failed: ${error.toString()}',
        failureType: PaymentFailureType.transactionError,
        stackTrace: stackTrace,
      ));
    }
  }

  /// Process a payment reversal/refund
  FutureEitherFailOr<String> processRefund({
    required String transactionId,
    required String patientMobile,
    required double amount,
    CancelToken? cancelToken,
  }) async {
    final timestamp = DateTime.now().toIso8601String();

    AppLogger()
        .info('Attempting to reverse payment for orderId: $transactionId');

    final payload = _createPayload(
      serviceName: cancelPurchase,
      patientMobile: patientMobile,
      amount: amount,
      transactionId: transactionId,
    );

    try {
      final response = await dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: EnvironmentConfig.hodanMerchantApiUrl,
        data: RequestData.json(payload),
        cancelToken: cancelToken,
      );

      if (response.data is Map<String, dynamic>) {
        final responseData = response.data as Map<String, dynamic>;
        final isSuccess = responseData['responseCode'] == '2001' ||
            responseData['responseCode'] == '2000';

        // ✅ Analytics logging
        await analytics.logPaymentInfo(
          currency: 'USD',
          paymentType: 'MWALLET_ACCOUNT',
          value: amount,
          parameters: {
            'event': isSuccess
                ? AnalyticsEvents.payment_refunded
                : AnalyticsEvents.payment_failed,
            'context': 'Refund',
            'transactionId': transactionId,
            'patientMobile': patientMobile,
            'timestamp': timestamp,
            'responseCode': responseData['responseCode'] ?? '',
            'responseMsg': responseData['responseMsg'] ?? '',
          },
        );

        if (isSuccess) {
          AppLogger().info('✅ Payment reversal successful');
          // return left(PaymentFailure(
          //   message:
          //       'Appointment failed but payment has been reversed. Please try again.',
          //   failureType: PaymentFailureType.transactionError,
          // ));
          return right('Reversal successful');
        }
      }

      AppLogger().error('❌ Payment reversal failed');

      return left(PaymentFailure(
        message:
            'Appointment failed and payment reversal failed. Please contact support.',
        failureType: PaymentFailureType.transactionError,
        statusCode: response.statusCode,
        apiMessage: response.data['responseMsg']?.toString(),
      ));
    } catch (error) {
      AppLogger().error('Error during payment reversal', error: error);

      // ❌ Analytics failure logging
      await analytics.logPaymentInfo(
        currency: 'USD',
        paymentType: 'MWALLET_ACCOUNT',
        value: amount,
        parameters: {
          'event': AnalyticsEvents.payment_failed,
          'context': 'Refund',
          'transactionId': transactionId,
          'patientMobile': patientMobile,
          'timestamp': timestamp,
          'error': error.toString(),
        },
      );

      return left(PaymentFailure(
        message: 'Failed to reverse payment: $error',
        failureType: PaymentFailureType.transactionError,
      ));
    }
  }

  /// Create a payload for the payment request
  Map<String, Object> _createPayload({
    required String serviceName,
    required String patientMobile,
    required double amount,
    String? transactionId, // Optional transactionId: Used for reversal
  }) {
    final serviceParams = {
      'merchantUid': EnvironmentConfig.hodanMerchantUid,
      // "merchantUid": EnvironmentConfig.rasiinMerchantUid,
      'apiUserId': EnvironmentConfig.hodanMerchantApiUserId,
      // "apiUserId": EnvironmentConfig.rasiinMerchantApiUserId,
      'apiKey': EnvironmentConfig.hodanMerchantApiKey,
      // "apiKey": EnvironmentConfig.rasiinMerchantApiKey,
      'paymentMethod': 'MWALLET_ACCOUNT',
      'payerInfo': {'accountNo': patientMobile},
    };

    // Add transaction-specific parameters based on service type
    if (serviceName == 'API_PURCHASE') {
      serviceParams['transactionInfo'] = {
        'referenceId': '********',
        'invoiceId': '********',
        'amount': amount,
        'currency': 'USD',
        'description': 'Medical Appointment Payment'
      };
    } else {
      // For API_CANCELPURCHASE
      serviceParams.addAll({
        'transactionId': transactionId ?? '',
        'referenceId': 'REF-${DateTime.now().millisecondsSinceEpoch}',
        'description': 'Reversal due to appointment saving failure'
      });
    }

    return {
      'schemaVersion': '1.0',
      'requestId': '***********',
      'timestamp': '2024-06-2 Africa',
      'channelName': 'WEB',
      'serviceName': serviceName,
      'serviceParams': serviceParams,
    };
  }

  String _extractTransactionId(dynamic responseData) {
    if (responseData is Map) {
      return responseData['params']?['transactionId']?.toString() ??
          responseData['transactionId']?.toString() ??
          'unknown';
    }
    return 'unknown';
  }
}
