import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:hodan_hospital/core/constants/analytics_events.dart';

class AnalyticsService {
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  FirebaseAnalyticsObserver get observer =>
      FirebaseAnalyticsObserver(analytics: _analytics);

  /// Log a generic custom event
  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logEvent(name: name, parameters: parameters);
  }

  /// Log screen views manually (if not using observer)
  Future<void> logScreenView({
    required String screenName,
    String? screenClassOverride,
  }) async {
    await _analytics.logScreenView(
      screenName: screenName,
      screenClass: screenClassOverride ?? screenName,
    );
  }

  /// Predefined examples
  Future<void> logNotificationOpened(String source) async {
    await logEvent(name: AnalyticsEvents.notification_opened, parameters: {
      'source': source,
    });
  }

  Future<void> logAppReviewOpened() async {
    await logEvent(name: AnalyticsEvents.app_review_prompt_shown);
  }

  Future<void> logAppRated() async {
    await logEvent(name: AnalyticsEvents.app_rated);
  }

  Future<void> logLogin({
    required String method,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logLogin(loginMethod: method, parameters: parameters);
  }

  Future<void> logRegister({
    required String method,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logSignUp(signUpMethod: method, parameters: parameters);
  }

  Future<void> logPaymentInfo({
    String? coupon,
    String? currency,
    String? paymentType,
    double? value,
    List<AnalyticsEventItem>? items,
    Map<String, Object>? parameters,
  }) async {
    await _analytics.logAddPaymentInfo(
      coupon: coupon,
      currency: currency,
      paymentType: paymentType,
      value: value,
      items: items,
      parameters: parameters,
    );
  }

  // Error tracking
  Future<void> logError({
    required String error,
    required StackTrace? stackTrace,
    String? context,
  }) async {
    if (kDebugMode) return;

    await _analytics.logEvent(
      name: AnalyticsEvents.error_occurred,
      parameters: {
        'error': error,
        'stack_trace': stackTrace.toString(),
        'context': context ?? '',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }
}
