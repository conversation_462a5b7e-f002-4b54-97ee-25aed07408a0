class AnalyticsEvents {
  // Auth
  static const String login = 'login';
  static const String register = 'register';
  static const String logout = 'logout';
  static const String otp_sent = 'otp_sent';
  static const String otp_verified = 'otp_verified';
  static const String otp_resend = 'otp_resend';
  static const String otp_error = 'otp_error';
  static const String onboarding_completed = 'onboarding_completed';

  // App Reviews
  static const String app_review_prompt_shown = 'app_review_prompt_shown';
  static const String app_rated = 'app_rated';

  // Errors
  static const String error_occurred = 'error_occurred';

  // Feedback
  static const String feedback_submitted = 'feedback_submitted';

  // Orders
  static const String appointment_booked = 'appointment_booked';
  static const String order_confirmed = 'order_confirmed';

  // Pdf & Printing
  static const String order_pdf_generated = 'order_pdf_generated';
  static const String order_pdf_downloaded = 'order_pdf_downloaded';
  static const String order_pdf_shared = 'order_pdf_shared';
  static const String order_pdf_printed = 'order_pdf_printed';
  static const String appointment_pdf_generated = 'appointment_pdf_generated';
  static const String appointment_pdf_downloaded = 'appointment_pdf_downloaded';
  static const String appointment_pdf_shared = 'appointment_pdf_shared';
  static const String appointment_pdf_printed = 'appointment_pdf_printed';
  static const String lab_result_pdf_generated = 'lab_result_pdf_generated';
  static const String lab_result_pdf_downloaded = 'lab_result_pdf_downloaded';
  static const String lab_result_pdf_shared = 'lab_result_pdf_shared';
  static const String lab_result_pdf_printed = 'lab_result_pdf_printed';

  // SMS & Notifications
  static const String sms_sent = 'sms_sent';
  static const String notification_sent = 'notification_sent';
  static const String notification_received = 'notification_received';
  static const String notification_clicked = 'notification_clicked';
  static const String notification_dismissed = 'notification_dismissed';
  static const String notification_error = 'notification_error';
  static const String notification_opened = 'notification_opened';
  static const String notification_tap_cold_start =
      'notification_tap_cold_start';
  static const String notification_opened_foreground =
      'notification_opened_foreground';
  static const String notification_opened_background =
      'notification_opened_background';

  // Payment Events
  static const String payment_initiated = 'payment_initiated';
  static const String payment_successful = 'payment_successful';
  static const String payment_failed = 'payment_failed';
  static const String payment_cancelled = 'payment_cancelled';
  static const String payment_refunded = 'payment_refunded';
}
