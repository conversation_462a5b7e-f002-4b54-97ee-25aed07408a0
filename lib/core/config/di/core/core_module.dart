import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hodan_hospital/core/config/di/dependency_injection.dart';
import 'package:hodan_hospital/core/database/database_manager.dart';
import 'package:hodan_hospital/core/errors/database_error_handler.dart';
import 'package:hodan_hospital/core/errors/http_error_handler.dart';
import 'package:hodan_hospital/core/network/connection_checker.dart';
import 'package:hodan_hospital/core/network/api_client/dio_api_client.dart';
import 'package:hodan_hospital/core/services/flutter_secure_storage_services.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/bottom%20nav%20cubit/bottom_nav_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/dialog%20cubit/dialog_cubit.dart';
import 'package:hodan_hospital/features/shared/presentation/blocs%20or%20cubits/theme-cubit/theme_storage.dart';
import 'package:injectable/injectable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';

import '../../../../features/shared/presentation/blocs or cubits/theme-cubit/theme_cubit.dart';
import '../../../services/analytics_service.dart';
import '../../../services/notification_services.dart';
import '../../../services/payment_service.dart';
import '../../../services/sms_services.dart';

@module
abstract class CoreModule {
  /// ✅ Database Manager (Pre-Resolved Singleton)
  @preResolve
  @lazySingleton
  Future<DatabaseManager> provideDatabaseManager() async {
    final databaseManager = DatabaseManager();
    await databaseManager.init();
    return databaseManager;
  }

  /// ✅ Secure Storage Service
  @lazySingleton
  FlutterSecureStorageServices provideSecureStorageServices() {
    return FlutterSecureStorageServices(
      flutterSecureStorage:
          const FlutterSecureStorage(), // <-- Explicitly instantiated
    );
  }

  /// ✅ Connection Checker (Singleton)
  @singleton
  ConnectionChecker provideConnectionChecker() {
    return ConnectionCheckerImpl(
      internetConnection: InternetConnection(),
    );
  }

  /// ✅ Dio API Client (Singleton)
  @lazySingleton
  DioApiClient get dioApiClient => DioApiClient();

  /// ✅ Http Error Handler
  @lazySingleton
  HttpErrorHandler provideHttpErrorHandler(
      ConnectionChecker connectionChecker) {
    return HttpErrorHandler(connectionChecker: connectionChecker);
  }

  /// ✅ Database Error Handler
  @lazySingleton
  DatabaseErrorHandler provideDatabaseErrorHandler(
      DatabaseManager databaseManager) {
    return DatabaseErrorHandler(databaseManager: databaseManager);
  }

  /// ✅ SMS Services
  @lazySingleton
  SmsServices get smsServices => SmsServices(
        flutterSecureStorageServices: sl(),
        dioApiClient: sl(),
      );

  /// ✅ Notification Service
  @lazySingleton
  NotificationService get notificationService => NotificationService();

  /// ✅ Analytics Service
  @lazySingleton
  AnalyticsService get analyticsService => AnalyticsService();

  @lazySingleton
  PaymentService get paymentService => PaymentService(
        dioApiClient: sl(),
        analytics: sl(),
      );

  /// ✅ ThemeStorage (Singleton)
  @lazySingleton
  ThemeStorage get themeStorage => ThemeStorage(
        flutterSecureStorageServices: sl(),
      );

  /// ✅ Blocs and Cubit
  @lazySingleton
  DialogCubit get dialogCubit => DialogCubit();

  @lazySingleton
  BottomNavCubit get bottomNavCubit => BottomNavCubit();

  @lazySingleton
  ThemeCubit get themeCubit => ThemeCubit(
        themeStorage: sl(),
      );
}
