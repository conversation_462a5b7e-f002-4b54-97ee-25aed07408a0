name: ho<PERSON>_hospital
description: "A modern doctor appointment booking app that allows patients to easily schedule medical consultations, manage appointments, and connect with healthcare providers."
publish_to: "none"

# Versioning Guide:
# version: MAJOR.MINOR.PATCH+BUILD
#
# - MAJOR: Big changes, breaking updates (e.g., 2.0.0)
# - MINOR: New features, backward compatible (e.g., 1.1.0)
# - PATCH: Small fixes, improvements (e.g., 1.0.1)
# - BUILD: Internal build number, always increases (e.g., +2)
#
# Example: version: 1.0.1+2 (Patch update, build 2)

# version: 1.0.0
version: 1.0.5+13

environment:
  sdk: ^3.6.1

dependencies:
  flutter:
    sdk: flutter

  # UI & Styling
  flutter_screenutil: ^5.9.3
  cupertino_icons: ^1.0.8
  font_awesome_flutter: ^10.8.0
  lottie: ^3.3.1
  shimmer: ^3.0.0
  awesome_snackbar_content: ^0.1.5
  smooth_page_indicator: ^1.2.1

  # Navigation & UI Enhancements
  go_router: ^14.8.1
  carousel_slider: ^5.0.0
  table_calendar: ^3.1.2

  # State Management & Dependency Injection
  flutter_bloc: ^9.1.0
  equatable: ^2.0.7
  get_it: ^8.0.3
  injectable: ^2.5.0

  # Networking & API Handling
  dio: ^5.7.0
  internet_connection_checker_plus: ^2.7.0

  # Data Storage & Persistence
  flutter_secure_storage: ^9.2.4
  # objectbox: ^4.0.3
  # objectbox_flutter_libs: ^4.0.3
  flutter_dotenv: ^5.2.1
  path_provider: ^2.1.5

  # Localization
  intl:

  # Utilities & Helpers
  fluttertoast: ^8.2.10
  logger: ^2.5.0
  fpdart: ^1.1.1
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2
  stream_transform: ^2.1.1
  confetti: ^0.8.0
  another_flushbar: ^1.12.30
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.17
  path: ^1.9.0
  mobile_scanner: ^6.0.6
  permission_handler: ^11.4.0
  printing: ^5.14.2

  # Package Info
  package_info_plus: ^8.3.0
  shorebird_code_push: ^2.0.3
  url_launcher: ^6.3.1
  # skeletonizer: ^1.4.3
  skeletonizer: ^2.0.1
  popover: ^0.3.1
  pinput: ^5.0.1
  device_info_plus: ^11.5.0
  flutter_rating_bar: ^4.0.1
  in_app_review: ^2.0.10
  firebase_core: ^4.0.0
  flutter_local_notifications: ^19.4.0
  firebase_messaging: ^16.0.0
  firebase_analytics: ^12.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.4.4
  flutter_gen_runner: ^5.9.0
  objectbox_generator: ^4.0.3
  injectable_generator: ^2.6.2
  build_runner: ^2.4.14

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icon/
    - assets/images/json/
    - assets/images/svg/
    - assets/images/png/
    - assets/images/jpeg/
    # - shorebird.yaml
    - .env.development
    - .env.production
